package org.jeecg.modules.crmfy.crmknowledgeitemtags.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 知识标签关联表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("crm_knowledge_item_tags")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_knowledge_item_tags对象", description="知识标签关联表")
public class CrmKnowledgeItemTags {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**知识条目ID*/
	@Excel(name = "知识条目ID", width = 15)
    @ApiModelProperty(value = "知识条目ID")
	private java.lang.Integer knowledgeId;
	/**标签ID*/
	@Excel(name = "标签ID", width = 15)
    @ApiModelProperty(value = "标签ID")
	private java.lang.Integer tagId;
	/**createdAt*/
    @ApiModelProperty(value = "createdAt")
	private java.util.Date createdAt;
}
