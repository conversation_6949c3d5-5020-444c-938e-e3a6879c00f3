package org.jeecg.modules.crmfy.crmknowledgetags.service.impl;

import org.jeecg.modules.crmfy.crmknowledgetags.entity.CrmKnowledgeTags;
import org.jeecg.modules.crmfy.crmknowledgetags.mapper.CrmKnowledgeTagsMapper;
import org.jeecg.modules.crmfy.crmknowledgetags.service.ICrmKnowledgeTagsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 知识标签
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class CrmKnowledgeTagsServiceImpl extends ServiceImpl<CrmKnowledgeTagsMapper, CrmKnowledgeTags> implements ICrmKnowledgeTagsService {

}
