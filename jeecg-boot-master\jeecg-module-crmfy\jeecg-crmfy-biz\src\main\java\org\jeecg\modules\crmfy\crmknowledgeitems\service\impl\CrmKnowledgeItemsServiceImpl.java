package org.jeecg.modules.crmfy.crmknowledgeitems.service.impl;

import org.jeecg.modules.crmfy.crmknowledgeitems.entity.CrmKnowledgeItems;
import org.jeecg.modules.crmfy.crmknowledgeitems.mapper.CrmKnowledgeItemsMapper;
import org.jeecg.modules.crmfy.crmknowledgeitems.service.ICrmKnowledgeItemsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 知识条目
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class CrmKnowledgeItemsServiceImpl extends ServiceImpl<CrmKnowledgeItemsMapper, CrmKnowledgeItems> implements ICrmKnowledgeItemsService {

}
