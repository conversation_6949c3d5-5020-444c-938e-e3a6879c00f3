package org.jeecg.modules.crmfy.crmknowledgecategories.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crmfy.crmknowledgecategories.entity.CrmKnowledgeCategories;
import org.jeecg.modules.crmfy.crmknowledgecategories.service.ICrmKnowledgeCategoriesService;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 知识分类
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Slf4j
@Api(tags="知识分类")
@RestController
@RequestMapping("/crmknowledgecategories/crmKnowledgeCategories")
public class CrmKnowledgeCategoriesController extends JeecgController<CrmKnowledgeCategories, ICrmKnowledgeCategoriesService> {
	@Autowired
	private ICrmKnowledgeCategoriesService crmKnowledgeCategoriesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param crmKnowledgeCategories
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "知识分类-分页列表查询")
	@ApiOperation(value="知识分类-分页列表查询", notes="知识分类-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CrmKnowledgeCategories crmKnowledgeCategories,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CrmKnowledgeCategories> queryWrapper = QueryGenerator.initQueryWrapper(crmKnowledgeCategories, req.getParameterMap());
		Page<CrmKnowledgeCategories> page = new Page<CrmKnowledgeCategories>(pageNo, pageSize);
		IPage<CrmKnowledgeCategories> pageList = crmKnowledgeCategoriesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 *
	 * @param crmKnowledgeCategories
	 * @return
	 */
	@AutoLog(value = "知识分类-添加")
	@ApiOperation(value="知识分类-添加", notes="知识分类-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CrmKnowledgeCategories crmKnowledgeCategories) {
		crmKnowledgeCategoriesService.save(crmKnowledgeCategories);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param crmKnowledgeCategories
	 * @return
	 */
	@AutoLog(value = "知识分类-编辑")
	@ApiOperation(value="知识分类-编辑", notes="知识分类-编辑")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody CrmKnowledgeCategories crmKnowledgeCategories) {
		crmKnowledgeCategoriesService.updateById(crmKnowledgeCategories);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "知识分类-通过id删除")
	@ApiOperation(value="知识分类-通过id删除", notes="知识分类-通过id删除")
	@GetMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		crmKnowledgeCategoriesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "知识分类-批量删除")
	@ApiOperation(value="知识分类-批量删除", notes="知识分类-批量删除")
	@GetMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.crmKnowledgeCategoriesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "知识分类-通过id查询")
	@ApiOperation(value="知识分类-通过id查询", notes="知识分类-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CrmKnowledgeCategories crmKnowledgeCategories = crmKnowledgeCategoriesService.getById(id);
		return Result.OK(crmKnowledgeCategories);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param crmKnowledgeCategories
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, CrmKnowledgeCategories crmKnowledgeCategories) {
      return super.exportXls(request, crmKnowledgeCategories, CrmKnowledgeCategories.class, "知识分类");
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, CrmKnowledgeCategories.class);
  }

}
