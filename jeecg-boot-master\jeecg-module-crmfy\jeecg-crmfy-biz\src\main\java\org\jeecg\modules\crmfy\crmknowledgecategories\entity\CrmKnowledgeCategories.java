package org.jeecg.modules.crmfy.crmknowledgecategories.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 知识分类
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("crm_knowledge_categories")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_knowledge_categories对象", description="知识分类")
public class CrmKnowledgeCategories {

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**分类名称*/
	@Excel(name = "分类名称", width = 15)
    @ApiModelProperty(value = "分类名称")
	private java.lang.String categoryName;
	/**分类编码*/
	@Excel(name = "分类编码", width = 15)
    @ApiModelProperty(value = "分类编码")
	private java.lang.String categoryCode;
	/**父分类ID*/
	@Excel(name = "父分类ID", width = 15)
    @ApiModelProperty(value = "父分类ID")
	private java.lang.Integer parentId;
	/**层级深度*/
	@Excel(name = "层级深度", width = 15)
    @ApiModelProperty(value = "层级深度")
	private java.lang.Integer categoryLevel;
	/**分类路径*/
	@Excel(name = "分类路径", width = 15)
    @ApiModelProperty(value = "分类路径")
	private java.lang.String path;
	/**分类描述*/
	@Excel(name = "分类描述", width = 15)
    @ApiModelProperty(value = "分类描述")
	private java.lang.Object description;
	/**图标*/
	@Excel(name = "图标", width = 15)
    @ApiModelProperty(value = "图标")
	private java.lang.String icon;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
	private java.lang.Integer sortOrder;
	/**创建人所属部门*/
	@Excel(name = "创建人所属部门", width = 15)
    @ApiModelProperty(value = "创建人所属部门")
	private java.lang.String sysOrgCode;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
	/**是否有子节点*/
	@TableField(exist = false)
    @ApiModelProperty(value = "是否有子节点")
	private boolean hasChildren;
}
