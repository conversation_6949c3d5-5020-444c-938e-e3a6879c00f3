package org.jeecg.modules.crmfy.crmknowledgeattachments.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 知识附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("crm_knowledge_attachments")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_knowledge_attachments对象", description="知识附件表")
public class CrmKnowledgeAttachments {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**知识条目ID*/
	@Excel(name = "知识条目ID", width = 15)
    @ApiModelProperty(value = "知识条目ID")
	private java.lang.Integer knowledgeId;
	/**文件名*/
	@Excel(name = "文件名", width = 15)
    @ApiModelProperty(value = "文件名")
	private java.lang.String filename;
	/**原始文件名*/
	@Excel(name = "原始文件名", width = 15)
    @ApiModelProperty(value = "原始文件名")
	private java.lang.String originalName;
	/**文件路径*/
	@Excel(name = "文件路径", width = 15)
    @ApiModelProperty(value = "文件路径")
	private java.lang.String filePath;
	/**文件大小(字节)*/
	@Excel(name = "文件大小(字节)", width = 15)
    @ApiModelProperty(value = "文件大小(字节)")
	private java.lang.Integer fileSize;
	/**文件类型*/
	@Excel(name = "文件类型", width = 15)
    @ApiModelProperty(value = "文件类型")
	private java.lang.String fileType;
	/**MIME类型*/
	@Excel(name = "MIME类型", width = 15)
    @ApiModelProperty(value = "MIME类型")
	private java.lang.String mimeType;
	/**文件描述*/
	@Excel(name = "文件描述", width = 15)
    @ApiModelProperty(value = "文件描述")
	private java.lang.Object description;
	/**下载次数*/
	@Excel(name = "下载次数", width = 15)
    @ApiModelProperty(value = "下载次数")
	private java.lang.Integer downloadCount;
	/**上传人ID*/
	@Excel(name = "上传人ID", width = 15)
    @ApiModelProperty(value = "上传人ID")
	private java.lang.Integer uploadedBy;
	/**createdAt*/
    @ApiModelProperty(value = "createdAt")
	private java.util.Date createdAt;
}
