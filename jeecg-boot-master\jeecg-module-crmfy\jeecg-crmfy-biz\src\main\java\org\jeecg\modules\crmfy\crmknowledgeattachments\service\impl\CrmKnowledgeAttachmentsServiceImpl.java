package org.jeecg.modules.crmfy.crmknowledgeattachments.service.impl;

import org.jeecg.modules.crmfy.crmknowledgeattachments.entity.CrmKnowledgeAttachments;
import org.jeecg.modules.crmfy.crmknowledgeattachments.mapper.CrmKnowledgeAttachmentsMapper;
import org.jeecg.modules.crmfy.crmknowledgeattachments.service.ICrmKnowledgeAttachmentsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 知识附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class CrmKnowledgeAttachmentsServiceImpl extends ServiceImpl<CrmKnowledgeAttachmentsMapper, CrmKnowledgeAttachments> implements ICrmKnowledgeAttachmentsService {

}
