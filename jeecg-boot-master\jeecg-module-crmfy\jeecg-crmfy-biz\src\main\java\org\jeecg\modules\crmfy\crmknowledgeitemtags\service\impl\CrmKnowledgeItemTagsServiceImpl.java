package org.jeecg.modules.crmfy.crmknowledgeitemtags.service.impl;

import org.jeecg.modules.crmfy.crmknowledgeitemtags.entity.CrmKnowledgeItemTags;
import org.jeecg.modules.crmfy.crmknowledgeitemtags.mapper.CrmKnowledgeItemTagsMapper;
import org.jeecg.modules.crmfy.crmknowledgeitemtags.service.ICrmKnowledgeItemTagsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 知识标签关联表
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class CrmKnowledgeItemTagsServiceImpl extends ServiceImpl<CrmKnowledgeItemTagsMapper, CrmKnowledgeItemTags> implements ICrmKnowledgeItemTagsService {

}
