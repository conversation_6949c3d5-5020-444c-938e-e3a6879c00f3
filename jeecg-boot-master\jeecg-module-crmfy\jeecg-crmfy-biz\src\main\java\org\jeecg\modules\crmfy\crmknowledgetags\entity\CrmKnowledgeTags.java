package org.jeecg.modules.crmfy.crmknowledgetags.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 知识标签
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("crm_knowledge_tags")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_knowledge_tags对象", description="知识标签")
public class CrmKnowledgeTags {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**标签名称*/
	@Excel(name = "标签名称", width = 15)
    @ApiModelProperty(value = "标签名称")
	private java.lang.String tagName;
	/**标签颜色*/
	@Excel(name = "标签颜色", width = 15)
    @ApiModelProperty(value = "标签颜色")
	private java.lang.String color;
	/**标签描述*/
	@Excel(name = "标签描述", width = 15)
    @ApiModelProperty(value = "标签描述")
	private java.lang.Object description;
	/**使用次数*/
	@Excel(name = "使用次数", width = 15)
    @ApiModelProperty(value = "使用次数")
	private java.lang.Integer usageCount;
	/**创建人ID*/
	@Excel(name = "创建人ID", width = 15)
    @ApiModelProperty(value = "创建人ID")
	private java.lang.Integer createdBy;
	/**createdAt*/
    @ApiModelProperty(value = "createdAt")
	private java.util.Date createdAt;
}
