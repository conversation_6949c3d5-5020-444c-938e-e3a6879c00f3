package org.jeecg.modules.crmfy.crmknowledgeitems.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 知识条目
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("crm_knowledge_items")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_knowledge_items对象", description="知识条目")
public class CrmKnowledgeItems {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.Integer id;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
	private java.lang.String title;
	/**副标题*/
	@Excel(name = "副标题", width = 15)
    @ApiModelProperty(value = "副标题")
	private java.lang.String subtitle;
	/**分类ID*/
	@Excel(name = "分类ID", width = 15)
    @ApiModelProperty(value = "分类ID")
	private java.lang.Integer categoryId;
	/**内容*/
	@Excel(name = "内容", width = 15)
    @ApiModelProperty(value = "内容")
	private java.lang.Object content;
	/**摘要*/
	@Excel(name = "摘要", width = 15)
    @ApiModelProperty(value = "摘要")
	private java.lang.Object summary;
	/**关键词*/
	@Excel(name = "关键词", width = 15)
    @ApiModelProperty(value = "关键词")
	private java.lang.String keywords;
	/**类型:1文章、2问答、3案例、4法规、5流程、6模板*/
	@Excel(name = "类型:1文章、2问答、3案例、4法规、5流程、6模板", width = 15)
    @ApiModelProperty(value = "类型:1文章、2问答、3案例、4法规、5流程、6模板")
	private java.lang.String type;
	/**难度等级:1基础、2中级、3高级*/
	@Excel(name = "难度等级:1基础、2中级、3高级", width = 15)
    @ApiModelProperty(value = "难度等级:1基础、2中级、3高级")
	private java.lang.String difficultyLevel;
	/**重要性等级:1低、2中、3高、4关键*/
	@Excel(name = "重要性等级:1低、2中、3高、4关键", width = 15)
    @ApiModelProperty(value = "重要性等级:1低、2中、3高、4关键")
	private java.lang.String importanceLevel;
	/**状态:0草稿1有效2下架*/
	@Excel(name = "状态:0草稿1有效2下架", width = 15)
    @ApiModelProperty(value = "状态:0草稿1有效2下架")
	private java.lang.String knowledgeStatus;
	/**是否公开*/
	@Excel(name = "是否公开", width = 15)
    @ApiModelProperty(value = "是否公开")
	private java.lang.Integer isPublic;
	/**浏览次数*/
	@Excel(name = "浏览次数", width = 15)
    @ApiModelProperty(value = "浏览次数")
	private java.lang.Integer viewCount;
	/**来源*/
	@Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源")
	private java.lang.String source;
	/**作者*/
	@Excel(name = "作者", width = 15)
    @ApiModelProperty(value = "作者")
	private java.lang.String author;
	/**生效日期*/
	@Excel(name = "生效日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "生效日期")
	private java.util.Date effectiveDate;
	/**失效日期*/
	@Excel(name = "失效日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "失效日期")
	private java.util.Date expiryDate;
	/**发布时间*/
	@Excel(name = "发布时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
	private java.util.Date publishedAt;
	/**创建人所属部门*/
	@Excel(name = "创建人所属部门", width = 15)
    @ApiModelProperty(value = "创建人所属部门")
	private java.lang.String sysOrgCode;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
}
