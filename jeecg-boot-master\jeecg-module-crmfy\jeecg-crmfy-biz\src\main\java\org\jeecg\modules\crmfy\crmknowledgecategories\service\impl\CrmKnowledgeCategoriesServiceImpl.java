package org.jeecg.modules.crmfy.crmknowledgecategories.service.impl;

import org.jeecg.modules.crmfy.crmknowledgecategories.entity.CrmKnowledgeCategories;
import org.jeecg.modules.crmfy.crmknowledgecategories.mapper.CrmKnowledgeCategoriesMapper;
import org.jeecg.modules.crmfy.crmknowledgecategories.service.ICrmKnowledgeCategoriesService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 知识分类
 * @Author: jeecg-boot
 * @Date:   2025-05-26
 * @Version: V1.0
 */
@Service
public class CrmKnowledgeCategoriesServiceImpl extends ServiceImpl<CrmKnowledgeCategoriesMapper, CrmKnowledgeCategories> implements ICrmKnowledgeCategoriesService {

}
